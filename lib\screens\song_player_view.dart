import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../models/media_item.dart';
import '../providers/app_provider.dart';
import '../services/media_service.dart';

class SongPlayerView extends StatefulWidget {
  final MediaItem item;
  const SongPlayerView({super.key, required this.item});

  @override
  State<SongPlayerView> createState() => _SongPlayerViewState();
}

class _SongPlayerViewState extends State<SongPlayerView> {
  @override
  void initState() {
    super.initState();
    // The media service will handle playback
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final app = Provider.of<AppProvider>(context, listen: false);
      app.playMedia(widget.item.id);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, app, child) {
        final mediaService = app.mediaService;

        return Scaffold(
          appBar: AppBar(title: Text(widget.item.title)),
          body: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const Spacer(),
                const Icon(Icons.music_note, size: 96),
                const SizedBox(height: 24),

                // Media info
                Text(
                  widget.item.title,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),
                if (widget.item.artist != null) ...[
                  const SizedBox(height: 8),
                  Text(
                    widget.item.artist!,
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white.withOpacity(0.7),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],

                const SizedBox(height: 32),

                // Progress slider and time
                ListenableBuilder(
                  listenable: mediaService,
                  builder: (context, child) {
                    final position = mediaService.position;
                    final duration = mediaService.duration;

                    return Column(
                      children: [
                        Slider(
                          value: position.inMilliseconds
                              .clamp(0, duration.inMilliseconds)
                              .toDouble(),
                          max: duration.inMilliseconds
                              .toDouble()
                              .clamp(1, double.infinity),
                          onChanged: (v) =>
                              mediaService.seek(Duration(milliseconds: v.toInt())),
                        ),
                        Text(
                          '${_format(position)} / ${_format(duration)}',
                          style: const TextStyle(color: Colors.white),
                        ),
                      ],
                    );
                  },
                ),

                const SizedBox(height: 16),

                // Playback controls
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    IconButton(
                      iconSize: 48,
                      icon: const Icon(Icons.replay_10),
                      onPressed: () async {
                        final pos = mediaService.position;
                        mediaService.seek(pos - const Duration(seconds: 10));
                      },
                    ),
                    const SizedBox(width: 16),
                    ListenableBuilder(
                      listenable: mediaService,
                      builder: (context, child) {
                        final playing = mediaService.isPlaying;
                        return FilledButton.icon(
                          onPressed: () => app.setPlaying(!playing),
                          icon: Icon(playing ? Icons.pause : Icons.play_arrow),
                          label: Text(playing ? 'Pause' : 'Play'),
                        );
                      },
                    ),
                    const SizedBox(width: 16),
                    IconButton(
                      iconSize: 48,
                      icon: const Icon(Icons.forward_30),
                      onPressed: () async {
                        final pos = mediaService.position;
                        mediaService.seek(pos + const Duration(seconds: 30));
                      },
                    ),
                  ],
                ),
                const Spacer(),
              ],
            ),
          ),
        );
      },
    );
  }

  String _format(Duration d) {
    String two(int n) => n.toString().padLeft(2, '0');
    final m = two(d.inMinutes.remainder(60));
    final s = two(d.inSeconds.remainder(60));
    final h = d.inHours;
    return h > 0 ? '$h:$m:$s' : '$m:$s';
  }
}
