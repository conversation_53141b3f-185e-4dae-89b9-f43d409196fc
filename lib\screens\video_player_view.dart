import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:video_player/video_player.dart';

import '../models/media_item.dart';
import '../providers/app_provider.dart';

class VideoPlayerView extends StatefulWidget {
  final MediaItem item;
  const VideoPlayerView({super.key, required this.item});

  @override
  State<VideoPlayerView> createState() => _VideoPlayerViewState();
}

class _VideoPlayerViewState extends State<VideoPlayerView> {
  ChewieController? _chewieController;

  @override
  void initState() {
    super.initState();
    // Start playing the video through the media service
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final app = Provider.of<AppProvider>(context, listen: false);
      app.playMedia(widget.item.id).then((_) {
        // Once the media service has initialized the video, create the Chewie controller
        _initializeChewie();
      });
    });
  }

  void _initializeChewie() {
    final app = Provider.of<AppProvider>(context, listen: false);
    final videoController = app.mediaService.videoController;

    if (videoController != null && videoController.value.isInitialized) {
      _chewieController = ChewieController(
        videoPlayerController: videoController,
        autoPlay: false, // Media service already handles play/pause
        looping: false,
        allowPlaybackSpeedChanging: true,
        allowMuting: true,
        allowFullScreen: true,
      );
      if (mounted) setState(() {});
    }
  }

  @override
  void dispose() {
    _chewieController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, app, child) {
        final mediaService = app.mediaService;
        final videoController = mediaService.videoController;

        return Scaffold(
          appBar: AppBar(title: Text(widget.item.title)),
          body: Center(
            child: _chewieController != null &&
                   videoController != null &&
                   videoController.value.isInitialized
                ? Chewie(controller: _chewieController!)
                : const CircularProgressIndicator(),
          ),
        );
      },
    );
  }
}


