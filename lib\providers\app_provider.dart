import 'package:flutter/foundation.dart';
import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';
import '../models/media_item.dart';
import '../models/playlist.dart';
import '../models/playback_state.dart';
import '../services/media_service.dart';

class AppProvider extends ChangeNotifier {
  final Uuid _uuid = const Uuid();
  final Random _random = Random();
  final MediaService _mediaService = MediaService();

  List<Playlist> playlists = [];
  List<MediaItem> library = [];
  PlaybackState playbackState = const PlaybackState();

  late File _mediaFile;
  late File _playlistsFile;
  late File _playbackStateFile;

  Future<void> loadInitial() async {
    final dir = await getApplicationDocumentsDirectory();
    _mediaFile = File('${dir.path}/media_items.json');
    _playlistsFile = File('${dir.path}/playlists.json');
    _playbackStateFile = File('${dir.path}/playback_state.json');

    if (_mediaFile.existsSync()) {
      final jsonList = jsonDecode(await _mediaFile.readAsString()) as List<dynamic>;
      library = jsonList.map((e) => MediaItem.fromJson(e)).toList();
    }
    
    if (_playlistsFile.existsSync()) {
      final jsonList = jsonDecode(await _playlistsFile.readAsString()) as List<dynamic>;
      playlists = jsonList.map((e) => Playlist.fromJson(e)).toList();
    }
    
    if (_playbackStateFile.existsSync()) {
      final json = jsonDecode(await _playbackStateFile.readAsString()) as Map<String, dynamic>;
      playbackState = PlaybackState.fromJson(json);
    }
    
    if (playlists.isEmpty) {
      await createPlaylist('Favorites');
    }
    notifyListeners();
  }

  // Media Library
  Future<MediaItem> addMedia({
    required String title,
    required String path,
    required bool isVideo,
    String? artist,
    String? album,
    String? genre,
  }) async {
    final MediaItem item = MediaItem(
      id: _uuid.v4(),
      title: title,
      path: path,
      isVideo: isVideo,
      artist: artist,
      album: album,
      genre: genre,
    );
    library = [...library, item];
    await _saveMedia();
    notifyListeners();
    return item;
  }

  Future<void> removeMedia(String mediaId) async {
    library = library.where((m) => m.id != mediaId).toList();
    for (final Playlist p in playlists) {
      p.mediaItemIds.remove(mediaId);
    }
    await _saveMedia();
    await _savePlaylists();
    notifyListeners();
  }

  MediaItem? getMedia(String id) => library.firstWhere((m) => m.id == id, orElse: () => null as MediaItem);

  // Playlists
  Future<Playlist> createPlaylist(String name, {String? description, String? coverColor}) async {
    final Playlist playlist = Playlist(
      id: _uuid.v4(),
      name: name,
      description: description,
      coverColor: coverColor ?? _generateRandomColor(),
      mediaItemIds: <String>[],
    );
    playlists = [...playlists, playlist];
    await _savePlaylists();
    notifyListeners();
    return playlist;
  }

  Future<void> renamePlaylist(String playlistId, String name) async {
    final idx = playlists.indexWhere((p) => p.id == playlistId);
    if (idx == -1) return;
    playlists[idx].name = name;
    playlists[idx].updatedAt = DateTime.now();
    await _savePlaylists();
    notifyListeners();
  }

  Future<void> updatePlaylistDescription(String playlistId, String? description) async {
    final idx = playlists.indexWhere((p) => p.id == playlistId);
    if (idx == -1) return;
    playlists[idx].description = description;
    playlists[idx].updatedAt = DateTime.now();
    await _savePlaylists();
    notifyListeners();
  }

  Future<void> deletePlaylist(String playlistId) async {
    playlists = playlists.where((p) => p.id != playlistId).toList();
    await _savePlaylists();
    if (playbackState.currentPlaylistId == playlistId) {
      playbackState = playbackState.copyWith(currentPlaylistId: null);
      await _savePlaybackState();
    }
    notifyListeners();
  }

  Future<void> addToPlaylist(String playlistId, String mediaId) async {
    final idx = playlists.indexWhere((p) => p.id == playlistId);
    if (idx == -1) return;
    if (!playlists[idx].mediaItemIds.contains(mediaId)) {
      playlists[idx].mediaItemIds.add(mediaId);
      playlists[idx].updatedAt = DateTime.now();
      await _savePlaylists();
    }
    notifyListeners();
  }

  Future<void> removeFromPlaylist(String playlistId, String mediaId) async {
    final idx = playlists.indexWhere((p) => p.id == playlistId);
    if (idx == -1) return;
    playlists[idx].mediaItemIds.remove(mediaId);
    playlists[idx].updatedAt = DateTime.now();
    await _savePlaylists();
    notifyListeners();
  }

  Future<void> reorderInPlaylist(String playlistId, int oldIndex, int newIndex) async {
    final idx = playlists.indexWhere((p) => p.id == playlistId);
    if (idx == -1) return;
    final list = playlists[idx].mediaItemIds;
    final String item = list.removeAt(oldIndex);
    list.insert(newIndex > oldIndex ? newIndex - 1 : newIndex, item);
    playlists[idx].updatedAt = DateTime.now();
    await _savePlaylists();
    notifyListeners();
  }

  // Playback Management
  Future<void> playPlaylist(String playlistId, {int startIndex = 0}) async {
    final playlist = playlists.firstWhere((p) => p.id == playlistId);
    final queue = List<String>.from(playlist.mediaItemIds);

    if (queue.isEmpty) return;

    final currentIndex = startIndex.clamp(0, queue.length - 1);
    final currentMediaId = queue[currentIndex];

    playbackState = playbackState.copyWith(
      queue: queue,
      currentIndex: currentIndex,
      currentMediaId: currentMediaId,
      currentPlaylistId: playlistId,
      isPlaying: true,
    );

    // Actually play the current media
    final media = getMedia(currentMediaId);
    if (media != null) {
      await _mediaService.playMedia(media);
    }

    await _savePlaybackState();
    notifyListeners();
  }

  Future<void> playMedia(String mediaId) async {
    final media = getMedia(mediaId);
    if (media != null) {
      // Update state
      playbackState = playbackState.copyWith(
        currentMediaId: mediaId,
        isPlaying: true,
      );

      // Actually play the media
      await _mediaService.playMedia(media);

      await _savePlaybackState();
      notifyListeners();
    }
  }

  Future<void> setPlaybackMode(PlaybackMode mode) async {
    playbackState = playbackState.copyWith(mode: mode);
    await _savePlaybackState();
    notifyListeners();
  }

  Future<void> nextTrack() async {
    if (!playbackState.hasNext) return;
    
    int nextIndex = playbackState.currentIndex + 1;
    if (playbackState.mode == PlaybackMode.shuffle) {
      nextIndex = _random.nextInt(playbackState.queue.length);
    }
    
    playbackState = playbackState.copyWith(
      currentIndex: nextIndex,
      currentMediaId: playbackState.queue[nextIndex],
    );
    
    await _savePlaybackState();
    notifyListeners();
  }

  Future<void> previousTrack() async {
    if (!playbackState.hasPrevious) return;
    
    int prevIndex = playbackState.currentIndex - 1;
    if (playbackState.mode == PlaybackMode.shuffle) {
      prevIndex = _random.nextInt(playbackState.queue.length);
    }
    
    playbackState = playbackState.copyWith(
      currentIndex: prevIndex,
      currentMediaId: playbackState.queue[prevIndex],
    );
    
    await _savePlaybackState();
    notifyListeners();
  }

  Future<void> updatePlaybackPosition(Duration position) async {
    playbackState = playbackState.copyWith(position: position);
    await _savePlaybackState();
    notifyListeners();
  }

  Future<void> updatePlaybackDuration(Duration duration) async {
    playbackState = playbackState.copyWith(duration: duration);
    await _savePlaybackState();
    notifyListeners();
  }

  Future<void> setPlaying(bool isPlaying) async {
    // Control actual playback
    if (isPlaying) {
      await _mediaService.play();
    } else {
      await _mediaService.pause();
    }

    playbackState = playbackState.copyWith(isPlaying: isPlaying);
    await _savePlaybackState();
    notifyListeners();
  }

  // Utility methods
  String _generateRandomColor() {
    final colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
    ];
    return colors[_random.nextInt(colors.length)];
  }

  List<MediaItem> getPlaylistItems(String playlistId) {
    final playlist = playlists.firstWhere((p) => p.id == playlistId);
    return playlist.mediaItemIds
        .map(getMedia)
        .whereType<MediaItem>()
        .toList();
  }

  MediaItem? getCurrentMedia() {
    if (playbackState.currentMediaId == null) return null;
    return getMedia(playbackState.currentMediaId!);
  }

  // Get the media service for direct access to playback controls
  MediaService get mediaService => _mediaService;

  // Save methods
  Future<void> _saveMedia() async {
    final data = library.map((m) => m.toJson()).toList();
    await _mediaFile.writeAsString(jsonEncode(data));
  }

  Future<void> _savePlaylists() async {
    final data = playlists.map((p) => p.toJson()).toList();
    await _playlistsFile.writeAsString(jsonEncode(data));
  }

  Future<void> _savePlaybackState() async {
    await _playbackStateFile.writeAsString(jsonEncode(playbackState.toJson()));
  }
}


