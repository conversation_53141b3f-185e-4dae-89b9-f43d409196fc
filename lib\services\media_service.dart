import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:just_audio/just_audio.dart';
import 'package:video_player/video_player.dart';

import '../models/media_item.dart';

class MediaService extends ChangeNotifier {
  static final MediaService _instance = MediaService._internal();
  factory MediaService() => _instance;
  MediaService._internal();

  // Audio player for songs
  AudioPlayer? _audioPlayer;
  
  // Video player for videos
  VideoPlayerController? _videoController;
  
  // Current media being played
  MediaItem? _currentMedia;
  
  // Playback state
  bool _isPlaying = false;
  Duration _position = Duration.zero;
  Duration _duration = Duration.zero;
  
  // Stream subscriptions
  StreamSubscription<Duration>? _positionSubscription;
  StreamSubscription<Duration?>? _durationSubscription;
  StreamSubscription<PlayerState>? _playerStateSubscription;
  
  // Getters
  MediaItem? get currentMedia => _currentMedia;
  bool get isPlaying => _isPlaying;
  Duration get position => _position;
  Duration get duration => _duration;
  bool get hasMedia => _currentMedia != null;
  
  // Initialize and play media
  Future<void> playMedia(MediaItem media) async {
    debugPrint('MediaService: Playing media - ${media.title} (${media.isVideo ? 'Video' : 'Audio'})');

    if (_currentMedia?.id == media.id && _isPlaying) {
      debugPrint('MediaService: Already playing this media');
      return; // Already playing this media
    }

    await _stopCurrentMedia();
    _currentMedia = media;

    try {
      if (media.isVideo) {
        debugPrint('MediaService: Initializing video player');
        await _initializeVideo(media);
      } else {
        debugPrint('MediaService: Initializing audio player');
        await _initializeAudio(media);
      }
      debugPrint('MediaService: Media initialized successfully');
      notifyListeners();
    } catch (e) {
      debugPrint('MediaService: Error playing media: $e');
      _currentMedia = null;
      notifyListeners();
      rethrow; // Re-throw to let the caller handle it
    }
  }
  
  Future<void> _initializeAudio(MediaItem media) async {
    _audioPlayer = AudioPlayer();
    
    // Set up listeners
    _positionSubscription = _audioPlayer!.positionStream.listen((position) {
      _position = position;
      notifyListeners();
    });
    
    _durationSubscription = _audioPlayer!.durationStream.listen((duration) {
      if (duration != null) {
        _duration = duration;
        notifyListeners();
      }
    });
    
    _playerStateSubscription = _audioPlayer!.playerStateStream.listen((state) {
      _isPlaying = state.playing;
      notifyListeners();
    });
    
    // Load and play the audio file
    await _audioPlayer!.setFilePath(media.path);
    await _audioPlayer!.play();
  }
  
  Future<void> _initializeVideo(MediaItem media) async {
    _videoController = VideoPlayerController.file(File(media.path));
    await _videoController!.initialize();
    
    // Set up listeners
    _videoController!.addListener(() {
      _position = _videoController!.value.position;
      _duration = _videoController!.value.duration;
      _isPlaying = _videoController!.value.isPlaying;
      notifyListeners();
    });
    
    await _videoController!.play();
  }
  
  // Playback controls
  Future<void> play() async {
    if (_audioPlayer != null) {
      await _audioPlayer!.play();
    } else if (_videoController != null) {
      await _videoController!.play();
    }
  }
  
  Future<void> pause() async {
    if (_audioPlayer != null) {
      await _audioPlayer!.pause();
    } else if (_videoController != null) {
      await _videoController!.pause();
    }
  }
  
  Future<void> seek(Duration position) async {
    if (_audioPlayer != null) {
      await _audioPlayer!.seek(position);
    } else if (_videoController != null) {
      await _videoController!.seekTo(position);
    }
  }
  
  Future<void> stop() async {
    await _stopCurrentMedia();
    _currentMedia = null;
    notifyListeners();
  }
  
  Future<void> _stopCurrentMedia() async {
    // Cancel subscriptions
    await _positionSubscription?.cancel();
    await _durationSubscription?.cancel();
    await _playerStateSubscription?.cancel();
    
    // Dispose players
    if (_audioPlayer != null) {
      await _audioPlayer!.stop();
      await _audioPlayer!.dispose();
      _audioPlayer = null;
    }
    
    if (_videoController != null) {
      await _videoController!.dispose();
      _videoController = null;
    }
    
    // Reset state
    _isPlaying = false;
    _position = Duration.zero;
    _duration = Duration.zero;
  }
  
  // Get video controller for video player widgets
  VideoPlayerController? get videoController => _videoController;
  
  @override
  void dispose() {
    _stopCurrentMedia();
    super.dispose();
  }
}
